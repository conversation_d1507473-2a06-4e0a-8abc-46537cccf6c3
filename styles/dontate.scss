@import 'colors';
@import 'fonts';

// Donate
.donate-legal-text {
  margin-right: 2.5rem;
  text-align: center; // Center-align text for better readability
}

.donate-tax {
  font-size: .9rem;
  font-style: italic;
  text-align: center;
  margin-top: 1rem; // Add spacing above for separation
}

.donation-text-grid {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2rem;
  color: #015e41;
  background: linear-gradient(135deg, #015e41, #4ade80);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.donorbox-images-container {
  display: none; // Hide on desktop, show on mobile
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap; // Allow images to wrap for smaller screens
}

.donorbox-container {
  flex: 0 0 auto;
  margin: 50px auto; // Center the donorbox
  padding: 1rem; // Add padding for spacing
  max-width: 100%; // Ensure it doesn't exceed the container width
  box-sizing: border-box; // Include padding in width calculation
}

.donate-image-container {
  column-gap: 5rem;
  display: flex;
  flex-direction: row;
  justify-content: center; // Center images
  flex-wrap: wrap; // Allow images to wrap for smaller screens
}

.donorbox-widget {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-bottom: 1.25rem;
}

.donate-use-image {
  //margin-left: 70px;
}

.donate-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  width: 100%;
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
  box-sizing: border-box;
}

.donate-grid-item {
  text-align: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 2rem 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #ffffff 0%, #f0f8f5 100%);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #015e41, #4ade80);
  }
}

.donate-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.donate-item-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #015e41;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.donate-grid-item p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #4a5568;
  margin: 0;
}

.donorbox-images-container-desktop {
  display: flex;
  height: 100px;
  justify-content: center;
  margin-bottom: 1rem;
  flex-wrap: wrap; // Allow images to wrap for smaller screens
}

.donate-image-desktop {
  height: 100px;
  width: auto;
  max-width: 100%;
}

@media (max-width: 768px) {
  .donorbox-images-container-desktop {
    display: none;
  }

  .donate-tax {
    font-size: .7rem;
    transform: scale(.6);
    transform-origin: left;
    width: 166.67%;
  }

  .donorbox-images-container {
    display: flex; // Show on mobile
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap; // Allow images to wrap for smaller screens
  }

  .donorbox-container {
    flex: 0 0 auto;
    margin-left: 0;
    margin-top: 1.25rem;
    max-width: 100%; // Ensure it doesn't exceed the container width
  }

  .donate-image-container {
    column-gap: 2.5rem;
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 1.25rem;
    flex-wrap: wrap; // Allow images to wrap for smaller screens
  }

  .donate-image {
    display: flex;
    height: auto;
    width: 25%;
    max-width: 100%; // Ensure image is responsive
  }

  .legal {
    margin-right: 2.5rem;
  }

  .donation-use {
    display: none;
  }

  .donate-grid-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin: 1rem auto;
    padding: 0 0.5rem;
  }

  .donate-grid-item {
    padding: 1.5rem 1rem;

    &:hover {
      transform: none; // Disable hover transform on mobile
    }
  }

  .donate-icon {
    font-size: 2.5rem;
  }

  .donate-item-title {
    font-size: 1.1rem;
  }
}

@media (min-width: 1024px) {
  .donation-text-grid {
    font-size: 1.5rem;
  }
}