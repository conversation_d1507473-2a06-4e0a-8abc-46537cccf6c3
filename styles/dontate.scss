@import 'colors';
@import 'fonts';

// Donate
.donate-legal-text {
  margin-right: 2.5rem;
  text-align: center; // Center-align text for better readability
}

.donate-tax {
  font-size: .9rem;
  font-style: italic;
  text-align: center;
  margin-top: 1rem; // Add spacing above for separation
}

.donation-text-grid {
  font-size: 2.5vw; // Use vw for responsive font size
  font-weight: bold;
  text-align: center;
  margin-bottom: 1rem;
}

.donorbox-images-container {
  display: none; // Hide on desktop, show on mobile
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap; // Allow images to wrap for smaller screens
}

.donorbox-container {
  flex: 0 0 auto;
  margin: 50px auto; // Center the donorbox
  padding: 1rem; // Add padding for spacing
  max-width: 100%; // Ensure it doesn't exceed the container width
  box-sizing: border-box; // Include padding in width calculation
}

.donate-image-container {
  column-gap: 5rem;
  display: flex;
  flex-direction: row;
  justify-content: center; // Center images
  flex-wrap: wrap; // Allow images to wrap for smaller screens
}

.donorbox-widget {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-bottom: 1.25rem;
}

.donate-use-image {
  //margin-left: 70px;
}

.donate-grid-container {
  display: grid;
  grid-template-columns: repeat(4, 190px);
  gap: 35px;
  width: 65%; // Ensure it takes full width of the container
  box-sizing: border-box; // Include padding in width calculation

}

.donate-grid-item {
  text-align: center;
  background-color: $light-gray; // Use your color scheme
  padding: 1rem; // Add padding for spacing
  border-radius: 8px; // Add rounded corners
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); // Add subtle shadow
  width: auto;
}

.donorbox-images-container-desktop {
  display: flex;
  height: 100px;
  justify-content: center;
  margin-bottom: 1rem;
  flex-wrap: wrap; // Allow images to wrap for smaller screens
}

.donate-image-desktop {
  height: 100px;
  width: auto;
  max-width: 100%;
}

@media (max-width: 768px) {
  .donorbox-images-container-desktop {
    display: none;
  }

  .donate-tax {
    font-size: .7rem;
    transform: scale(.6);
    transform-origin: left;
    width: 166.67%;
  }

  .donorbox-images-container {
    display: flex; // Show on mobile
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap; // Allow images to wrap for smaller screens
  }

  .donorbox-container {
    flex: 0 0 auto;
    margin-left: 0;
    margin-top: 1.25rem;
    max-width: 100%; // Ensure it doesn't exceed the container width
  }

  .donate-image-container {
    column-gap: 2.5rem;
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 1.25rem;
    flex-wrap: wrap; // Allow images to wrap for smaller screens
  }

  .donate-image {
    display: flex;
    height: auto;
    width: 25%;
    max-width: 100%; // Ensure image is responsive
  }

  .legal {
    margin-right: 2.5rem;
  }

  .donation-use {
    display: none;
  }
}

@media (min-width: 1024px) {
  .donation-text-grid {
    font-size: 1.5rem;
  }
}